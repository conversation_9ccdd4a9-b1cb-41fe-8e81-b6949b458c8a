/**
 * JWT 工具模块
 * 使用 jose 库实现 JWT 功能
 * 优化版本：使用 Bun 特有的 API 和性能特性
 */
import { SignJWT, jwtVerify } from 'jose';
import config from './config';

// 创建签名密钥
const secretKey = new TextEncoder().encode(config.jwt.secret);

// 使用 Bun 的 crypto API 生成 UUID
function generateUUID(): string {
	return crypto.randomUUID();
}

/**
 * 创建 JWT 令牌
 * @param payload - 令牌负载
 * @returns Promise<string> - JWT 令牌
 */
export async function createToken(payload: Record<string, any>): Promise<string> {
	const jti = generateUUID(); // 唯一标识符
	const iat = Math.floor(Date.now() / 1000); // 签发时间
	const exp = iat + config.jwt.expiresIn; // 过期时间

	const token = await new SignJWT({ ...payload })
		.setProtectedHeader({ alg: 'HS256' })
		.setJti(jti)
		.setIssuedAt(iat)
		.setExpirationTime(exp)
		.sign(secretKey);

	return token;
}

/**
 * 验证 JWT 令牌
 * @param token - JWT 令牌
 * @returns Promise<{payload: any, valid: boolean, remaining?: number}> - 验证结果
 */
export async function verifyToken(token: string): Promise<{ payload: any, valid: boolean, remaining?: number }> {
	try {
		const { payload } = await jwtVerify(token, secretKey);
		const now = Math.floor(Date.now() / 1000);
		const exp = payload.exp as number;

		// 计算剩余有效期（秒）
		const remaining = exp - now;

		return {
			payload,
			valid: true,
			remaining
		};
	} catch (error) {
		return {
			payload: null,
			valid: false
		};
	}
}

/**
 * 刷新 JWT 令牌
 * @param token - 现有 JWT 令牌
 * @returns Promise<{token: string, created: boolean}> - 新令牌和是否创建新令牌的标志
 */
export async function refreshToken(token: string): Promise<{ token: string, created: boolean, valid?: boolean }> {
	try {
		const { payload, valid } = await verifyToken(token);

		if (valid) {
			// 保留原始负载，但更新过期时间
			const newToken = await createToken({
				sub: payload.sub,
				deviceId: payload.deviceId,
				clientIp: payload.clientIp,
				type: payload.type
			});

			return {
				token: newToken,
				created: true,
				valid: true
			};
		}
	} catch (error) {
		// 令牌无效，将在下面创建新令牌
	}

	// 如果令牌无效或验证失败，创建一个新的令牌
	const newToken = await createToken({
		sub: generateUUID(),
		type: 'session'
	});

	return {
		token: newToken,
		created: true,
		valid: false
	};
}
