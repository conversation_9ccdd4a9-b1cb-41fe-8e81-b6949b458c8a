/**
 * 应用程序类型定义
 */

// 用户行为事件类型
export interface Event {
	userId?: string;
	eventType: string;
	eventData: {
		metadata?: {
			url?: string;
			referrer?: string;
			userAgent?: string;
			[key: string]: any;
		};
		[key: string]: any;
	};
	timestamp?: string;
	sessionId?: string;
	deviceId?: string;
}

// 批量事件请求
export interface BatchEventRequest {
	events: Event[];
}

// 会话操作类型
export type SessionAction = 'create' | 'refresh' | 'verify';

// 会话请求
export interface SessionRequest {
	action: SessionAction;
	deviceId?: string;
	token?: string;
}

// 会话响应
export interface SessionResponse {
	action: SessionAction;
	token?: string;
	expiresIn?: number;
	created?: boolean;
	valid?: boolean;
	remaining?: number;
}

// 限流缓存项
export interface RateLimitCacheItem {
	count: number;
	resetTime: number;
}

// 限流设置
export interface RateLimitSettings {
	windowMs: number;
	maxRequests: number;
}

// 客户端地址信息
export interface ClientAddress {
	address: string;
	port: number;
}

// 服务器指标
export interface ServerMetrics {
	masterPid: number;
	workers: any[];
	aggregated: {
		cpu: string;
		memory: number;
		totalRequests: number;
	};
}
