/**
 * 测试 tracker 模块的零拷贝优化
 */
import { logEvent, flushEvents, shutdown } from "../src/tracker";
import { existsSync, mkdirSync, statSync } from "node:fs";
import { join } from "node:path";

// 确保日志目录存在
const logDir = join(process.cwd(), "logs", "behavior");
console.log(`检查日志目录: ${logDir}`);
if (!existsSync(logDir)) {
    console.log(`创建日志目录: ${logDir}`);
    mkdirSync(logDir, { recursive: true });
}

// 生成测试事件
function generateTestEvent(id: number) {
    return {
        id,
        type: "page_view",
        url: "/test-page",
        userId: `user-${Math.floor(Math.random() * 1000)}`,
        sessionId: `session-${Math.floor(Math.random() * 1000)}`,
        userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        referrer: "https://example.com",
        metadata: {
            key1: "value1",
            key2: "value2",
            key3: "value3",
            // 添加一些随机数据使事件大小更真实
            randomData: Array.from({ length: 5 }, () => Math.random().toString(36).substring(2))
        }
    };
}

// 测试批量记录事件
async function testBatchLogging(count: number) {
    console.log(`开始测试批量记录 ${count} 个事件...`);

    const startTime = performance.now();

    try {
        // 批量记录事件
        for (let i = 0; i < count; i++) {
            await logEvent(generateTestEvent(i));
        }
        console.log(`已记录 ${count} 个事件...`);

        console.log(`手动刷新事件...`);
        // 手动刷新一次，确保所有事件都被写入
        await flushEvents();

        console.log(`所有事件已记录，正在关闭...`);
        // 确保所有事件都被刷新到磁盘
        await shutdown();

        const endTime = performance.now();
        const duration = endTime - startTime;

        console.log(`完成记录 ${count} 个事件，耗时: ${duration.toFixed(2)}ms`);
        console.log(`平均每个事件耗时: ${(duration / count).toFixed(2)}ms`);
    } catch (error) {
        console.error("测试过程中发生错误:", error);
    }
}

// 运行测试
async function runTests() {
    console.log("开始运行测试...");
    // 测试不同批量大小，包括较大的批量
    await testBatchLogging(1);
    await testBatchLogging(5000);
    console.log("测试完成!");
}

// 执行测试
console.log("启动测试程序...");
runTests().catch(err => {
    console.error("测试程序出错:", err);
});
