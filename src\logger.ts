/**
 * 程序日志模块
 * 基于 Pino 高性能日志框架，使用其内置的日志轮转功能
 */
import { join } from "node:path";
import { mkdir } from "node:fs/promises";
import { existsSync } from "node:fs";
import pino from "pino";
import config from "./config";

// 确保日志目录存在
async function ensureLogDirectory() {
	if (!existsSync(config.logging.directory)) {
		try {
			await mkdir(config.logging.directory, { recursive: true });
		} catch (err: any) {
			console.error(`创建程序日志目录失败: ${err.message}`);
			process.exit(1);
		}
	}
}

// 立即执行确保日志目录存在
ensureLogDirectory();

// 创建日志文件路径
const logFilePath = join(config.logging.directory, `${config.logging.filename.replace("%DATE%", new Date().toISOString().split('T')[0])}`);

// 确保日志目录存在
ensureLogDirectory().then(() => {
	console.log(`程序日志将写入到: ${logFilePath}`);
});

// 创建 Pino 日志实例，使用 Pino 的内置轮转功能
const pinoInstance = pino({
	level: config.server.development ? "debug" : "info",
	timestamp: pino.stdTimeFunctions.isoTime
}, pino.destination({
	dest: logFilePath,
	sync: false, // 异步写入
	// 使用 Pino 的内置轮转功能
	mkdir: true, // 自动创建目录
	// 文件大小轮转
	maxSize: config.logging.size.endsWith('M')
		? parseInt(config.logging.size.slice(0, -1), 10) * 1024 * 1024
		: 50 * 1024 * 1024, // 默认 50MB
	// 文件数量限制
	maxFiles: config.logging.maxFiles
}));

/**
 * 日志工具函数
 * 使用 Pino 进行日志记录，但根据环境决定是否输出
 */
const logger = {
	// 调试日志，只在开发环境输出
	dev: (message: string, ...args: any[]) => {
		if (config.server.development) {
			pinoInstance.debug(message, ...args);
		}
	},

	// 信息日志，所有环境都输出
	info: (message: string, ...args: any[]) => {
		pinoInstance.info(message, ...args);
	},

	// 警告日志，所有环境都输出
	warn: (message: string, ...args: any[]) => {
		pinoInstance.warn(message, ...args);
	},

	// 错误日志，所有环境都输出
	error: (message: string, ...args: any[]) => {
		pinoInstance.error(message, ...args);
	}
};

/**
 * 确保在关闭前刷新所有日志
 */
function shutdown(): Promise<void> {
	return new Promise((resolve) => {
		// 记录关闭信息
		logger.info("正在关闭程序日志系统...");

		// 写入最后一条关闭日志
		logger.info("程序日志系统已安全关闭");

		// 等待 Pino 完成写入
		pinoInstance.flush(() => {
			resolve();
		});
	});
}

// 确保在进程退出时刷新日志
process.on("beforeExit", () => {
	logger.info("进程即将退出，正在刷新程序日志...");
	pinoInstance.flush();
});

// 导出模块
export {
	shutdown,
	logger,
	pinoInstance,
};
