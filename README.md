# xy-admin-bun

一个基于 Bun 运行时和 TypeScript 开发的高吞吐量、低延迟用户行为跟踪服务器，专为处理高并发场景而设计。

## 项目概述

这是一个基于 Bun 运行时和 TypeScript 开发的用户行为跟踪服务器，可以接收客户端发送的用户行为数据（如页面浏览、点击、表单提交等），并将这些数据以日志文件的形式存储在本地。系统设计为高吞吐量、低延迟，能够处理高并发请求。相比 Node.js，Bun 提供了更高的性能和更低的内存占用。

## 主要特性

- 基于 Bun 的高性能 HTTP 服务器，提供更高的吞吐量和更低的延迟
- 使用 TypeScript 进行类型安全的开发
- 高效的日志管理，支持文件轮转和压缩
- 批量处理提高吞吐量
- 基于 JSON 的事件格式
- 简单易用的 REST API
- 基于 JWT 的会话管理
- 优雅的关闭和错误处理机制
- 内置限流保护

## 项目文件结构

项目包含以下主要文件：

1. **server.ts** - 主服务器入口文件
2. **config.ts** - 配置设置
3. **logger.ts** - 日志处理和文件写入模块
4. **jwt.ts** - JWT 认证模块
5. **types.ts** - TypeScript 类型定义
6. **test.ts** - 性能测试脚本
7. **test-simple.ts** - 简单功能测试脚本

## 安装

```bash
# 克隆仓库
git clone <repository-url>
cd xy-admin-bun

# 安装依赖
bun install
```

## 使用方法

### 启动服务器

```bash
# 开发模式（带热重载）
bun dev

# 生产模式
bun start

# 构建项目
bun build
```

### API 端点

#### 健康检查

```
GET /health
```

返回服务器状态和 JWT 令牌，用于后续请求认证。

#### 会话管理

```
POST /session
```

用于创建、刷新或验证 JWT 令牌。

请求体示例：

```json
{
  "action": "create",
  "deviceId": "device-123"
}
```

支持的操作：
- `create`: 创建新的 JWT 令牌
- `refresh`: 刷新现有 JWT 令牌
- `verify`: 验证 JWT 令牌的有效性

#### 跟踪单个事件

```
POST /track
```

请求体示例：

```json
{
  "userId": "user123",
  "eventType": "pageView",
  "eventData": {
    "metadata": {
      "url": "https://example.com/page1",
      "referrer": "https://example.com",
      "userAgent": "Mozilla/5.0"
    }
  }
}
```

#### 批量跟踪事件

```
POST /track/batch
```

请求体示例：

```json
{
  "events": [
    {
      "userId": "user123",
      "eventType": "pageView",
      "eventData": {
        "metadata": {
          "url": "https://example.com/page1"
        }
      }
    },
    {
      "userId": "user123",
      "eventType": "click",
      "eventData": {
        "metadata": {
          "element": "button"
        }
      }
    }
  ]
}
```

#### 内部指标

```
GET /internal-metrics
```

返回服务器内部指标，如 CPU 使用率、内存使用和请求计数。

## 性能测试

运行性能测试脚本：

```bash
bun test
```

## 与原始 xy-admin 的区别

相比原始的基于 Node.js 和 Fastify 的 xy-admin 项目，xy-admin-bun 有以下改进：

1. **更高性能**：利用 Bun 的高性能特性，提供更高的吞吐量和更低的延迟
2. **类型安全**：使用 TypeScript 进行开发，提供更好的类型安全和开发体验
3. **更低内存占用**：Bun 的内存占用比 Node.js 更低
4. **更简洁的代码**：利用 Bun 的内置 API，减少了对第三方库的依赖
5. **更快的启动时间**：Bun 的启动时间比 Node.js 更快

## 许可证

ISC
