{"name": "xy-admin-bun", "version": "1.0.0", "description": "High-throughput, low-latency user behavior tracking server using Bun runtime", "type": "module", "scripts": {"dev": "bun run src/server.ts", "dev:watch": "bun --watch run src/server.ts", "start": "bun run dist/server.js", "build": "bun build src/server.ts --outdir ./dist --target bun", "build:prod": "bun build src/server.ts --outdir ./dist --target bun --minify", "build:exe": "bun build src/server.ts --compile --outfile xy-tracker", "build:config": "bun run build.config.ts"}, "keywords": ["user-behavior", "tracking", "high-throughput", "low-latency", "bun", "typescript"], "author": "", "license": "ISC", "devDependencies": {"bun-types": "latest", "typescript": "latest"}, "dependencies": {"jose": "^5.2.0", "pino": "^9.6.0", "rotating-file-stream": "^3.2.6"}}