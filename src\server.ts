/**
 * 用户行为跟踪服务器入口点
 * 使用 Bun.serve 实现高性能 HTTP 服务器
 * 结合 Pino 高性能日志框架和 Bun 的文件系统 API
 */
import { Server } from "bun";
import { randomUUID } from "crypto";
import { shutdown, logger } from "./logger";
import { logEvent, shutdown as shutdownBehaviorLogger } from "./tracker";
import config from "./config";
import { createToken, verifyToken, refreshToken } from "./jwt";
import type { Event, BatchEventRequest, SessionRequest, RateLimitCacheItem } from "./types";

/**
 * CORS配置
 */
const CORS_HEADERS = {
	"Access-Control-Allow-Origin": "*", // 允许所有来源访问，生产环境中应该限制为特定域名
	"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
	"Access-Control-Allow-Headers": "Content-Type, Authorization, X-Device-ID",
	"Access-Control-Max-Age": "86400" // 预检请求结果缓存24小时
};

/**
 * 添加CORS头到响应
 * @param response - 原始响应对象
 * @returns 添加了CORS头的新响应对象
 */
function addCorsHeaders(response: Response): Response {
	const headers = new Headers(response.headers);

	// 添加CORS头
	Object.entries(CORS_HEADERS).forEach(([key, value]) => {
		headers.set(key, value);
	});

	// 创建新的响应对象，保留原始响应的状态和正文
	return new Response(response.body, {
		status: response.status,
		statusText: response.statusText,
		headers
	});
}

/**
 * 处理OPTIONS预检请求
 * @returns 带有CORS头的空响应
 */
function handleOptionsRequest(): Response {
	return new Response(null, {
		status: 204, // No Content
		headers: CORS_HEADERS
	});
}

// 使用从 logger.ts 导入的 pinoLogger 对象

// 用于限流的 Map
// 键: 客户端标识符 (设备ID或IP地址)
// 值: { count: 请求次数, resetTime: 重置时间 }
const rateLimitCache = new Map<string, RateLimitCacheItem>();

// 限流设置
const RATE_LIMIT = {
	windowMs: 60 * 1000, // 1分钟窗口
	maxRequests: 30      // 每个窗口最多30次请求
};

// 用于跟踪总请求数的计数器
let totalRequestsSinceStart = 0;

// 清理过期限流记录的间隔（毫秒），默认1小时
const CLEANUP_INTERVAL = 60 * 60 * 1000;

// 定期清理过期的限流记录
setInterval(() => {
	const now = Date.now();

	// 清理过期的限流记录
	for (const [key, rateLimit] of rateLimitCache.entries()) {
		if (now > rateLimit.resetTime) {
			rateLimitCache.delete(key);
		}
	}
}, CLEANUP_INTERVAL);

/**
 * 应用限流逻辑
 * @param clientId - 客户端标识符
 * @returns {object|null} - 如果限流，返回错误对象；否则返回null
 */
function applyRateLimit(clientId: string): { error: string; retryAfter: number } | null {
	const now = Date.now();
	const limitKey = clientId;

	if (rateLimitCache.has(limitKey)) {
		const rateLimit = rateLimitCache.get(limitKey)!;

		if (now > rateLimit.resetTime) {
			rateLimit.count = 1;
			rateLimit.resetTime = now + RATE_LIMIT.windowMs;
		} else {
			rateLimit.count++;

			if (rateLimit.count > RATE_LIMIT.maxRequests) {
				return {
					error: '请求过于频繁，请稍后再试',
					retryAfter: Math.ceil((rateLimit.resetTime - now) / 1000)
				};
			}
		}
	} else {
		rateLimitCache.set(limitKey, {
			count: 1,
			resetTime: now + RATE_LIMIT.windowMs
		});
	}

	return null;
}

// 创建 Bun HTTP 服务器
const server = Bun.serve({
	port: config.server.port,
	hostname: config.server.host,
	development: config.server.development,

	// 路由处理
	routes: {
		// 健康检查端点
		"/health": async (req, server) => {
			// 处理OPTIONS预检请求
			if (req.method === "OPTIONS") {
				return handleOptionsRequest();
			}

			// 获取客户端标识符（优先使用请求中的deviceId，其次使用IP地址）
			const url = new URL(req.url);
			const deviceId = url.searchParams.get("deviceId") || req.headers.get("x-device-id");
			const clientId = deviceId || server.requestIP(req)?.address || '未知客户端';
			const now = Date.now();

			// 应用限流逻辑
			const rateLimitResult = applyRateLimit(clientId);
			if (rateLimitResult) {
				return addCorsHeaders(Response.json(rateLimitResult, {
					status: 429,
					headers: {
						'Retry-After': rateLimitResult.retryAfter.toString()
					}
				}));
			}

			// 创建JWT令牌作为会话ID
			// 包含设备ID和创建时间等信息
			const tokenPayload = {
				sub: clientId, // subject (用户/设备标识)
				iat: Math.floor(now / 1000), // issued at (签发时间)
				deviceId: deviceId || null,
				clientIp: server.requestIP(req)?.address
			};

			// 签发JWT令牌
			const token = await createToken(tokenPayload);

			logger.dev(`为客户端 ${clientId} 创建JWT令牌`);

			// 返回健康状态和JWT令牌，并添加CORS头
			return addCorsHeaders(Response.json({
				status: 'ok',
				pid: process.pid,
				totalRequests: totalRequestsSinceStart,
				token, // JWT令牌
				expiresIn: config.jwt.expiresIn // 过期时间（秒）
			}));
		},

		// 会话管理端点
		"/session": async (req) => {
			// 处理OPTIONS预检请求
			if (req.method === "OPTIONS") {
				return handleOptionsRequest();
			}

			if (req.method !== "POST") {
				return addCorsHeaders(new Response("Method Not Allowed", { status: 405 }));
			}

			try {
				const body = await req.json() as SessionRequest;
				// 获取客户端标识符（优先使用请求中的deviceId，其次使用IP地址）
				const deviceId = body.deviceId || req.headers.get("x-device-id");
				const clientIp = server.requestIP(req)?.address;
				const clientId = deviceId || clientIp || '未知客户端';
				const now = Date.now();
				const action = body.action || 'create';

				// 应用限流逻辑
				const limitKey = deviceId || clientIp;
				if (limitKey) {
					const rateLimitResult = applyRateLimit(limitKey);
					if (rateLimitResult) {
						return addCorsHeaders(Response.json(rateLimitResult, {
							status: 429,
							headers: {
								'Retry-After': rateLimitResult.retryAfter.toString()
							}
						}));
					}
				}

				let result;

				switch (action) {
					case 'create':
						// 创建新JWT令牌
						const createPayload = {
							sub: clientId,
							iat: Math.floor(now / 1000),
							deviceId: deviceId || null,
							clientIp,
							type: 'session'
						};

						const token = await createToken(createPayload);
						logger.dev(`客户端 ${clientId} 主动创建新JWT令牌`);
						result = {
							action: 'create',
							token,
							expiresIn: config.jwt.expiresIn,
							created: true
						};
						break;

					case 'refresh':
						// 刷新JWT令牌
						// 从请求体或Authorization头获取令牌
						let currentToken = body.token;
						if (!currentToken && req.headers.has('Authorization')) {
							const authHeader = req.headers.get('Authorization') || '';
							if (authHeader.startsWith('Bearer ')) {
								currentToken = authHeader.substring(7);
							}
						}

						if (!currentToken) {
							// 如果没有提供令牌，创建一个新的
							const newToken = await createToken({
								sub: clientId,
								deviceId: deviceId || null,
								clientIp,
								type: 'session'
							});
							result = {
								action: 'refresh',
								token: newToken,
								expiresIn: config.jwt.expiresIn,
								created: true
							};
						} else {
							// 刷新现有令牌
							const refreshResult = await refreshToken(currentToken);
							result = {
								action: 'refresh',
								token: refreshResult.token,
								expiresIn: config.jwt.expiresIn,
								created: refreshResult.created
							};
						}
						break;

					case 'verify':
						// 验证JWT令牌
						// 从请求体或Authorization头获取令牌
						let tokenToVerify = body.token;
						if (!tokenToVerify && req.headers.has('Authorization')) {
							const authHeader = req.headers.get('Authorization') || '';
							if (authHeader.startsWith('Bearer ')) {
								tokenToVerify = authHeader.substring(7);
							}
						}

						if (!tokenToVerify) {
							result = {
								action: 'verify',
								valid: false
							};
						} else {
							const verifyResult = await verifyToken(tokenToVerify);
							result = {
								action: 'verify',
								valid: verifyResult.valid,
								remaining: verifyResult.remaining
							};
						}
						break;

					default:
						return addCorsHeaders(Response.json({
							error: '无效的操作，支持的操作有: create, refresh, verify'
						}, { status: 400 }));
				}

				return addCorsHeaders(Response.json(result));
			} catch (error) {
				logger.error('处理会话请求时出错:', error);
				return addCorsHeaders(Response.json({ error: '内部服务器错误' }, { status: 500 }));
			}
		},

		// 跟踪单个事件的端点
		"/track": async (req) => {
			// 处理OPTIONS预检请求
			if (req.method === "OPTIONS") {
				return handleOptionsRequest();
			}

			if (req.method !== "POST") {
				return addCorsHeaders(new Response("Method Not Allowed", { status: 405 }));
			}

			try {
				// 验证JWT令牌
				const authHeader = req.headers.get('Authorization') || '';
				if (!authHeader.startsWith('Bearer ')) {
					return addCorsHeaders(Response.json({ error: '未授权，请先获取有效的会话ID' }, { status: 401 }));
				}

				const token = authHeader.substring(7);
				const { valid, payload } = await verifyToken(token);

				if (!valid) {
					return addCorsHeaders(Response.json({ error: '无效的会话ID，请刷新' }, { status: 401 }));
				}

				const event = await req.json() as Event;

				// 验证必填字段
				if (!event.eventType) {
					return addCorsHeaders(Response.json({
						error: '缺少必填字段：eventType是必需的'
					}, { status: 400 }));
				}

				// 从JWT令牌中获取会话信息
				const sessionId = payload.jti || payload.sub;
				const deviceId = payload.deviceId;
				const timestamp = new Date().toISOString();

				// 构建完整的事件对象
				const fullEvent = {
					...event,
					sessionId, // 使用JWT令牌中的会话ID
					deviceId: deviceId || event.deviceId, // 优先使用JWT中的设备ID
					timestamp: event.timestamp || timestamp
				};

				// 记录用户行为事件
				await logEvent(fullEvent);

				return addCorsHeaders(Response.json({ status: 'accepted' }, { status: 202 }));
			} catch (error) {
				logger.error('处理事件时出错:', error);
				return addCorsHeaders(Response.json({ error: '内部服务器错误' }, { status: 500 }));
			}
		},

		// 批量跟踪多个事件的端点
		"/track/batch": async (req) => {
			// 处理OPTIONS预检请求
			if (req.method === "OPTIONS") {
				return handleOptionsRequest();
			}

			if (req.method !== "POST") {
				return addCorsHeaders(new Response("Method Not Allowed", { status: 405 }));
			}

			try {
				// 验证JWT令牌
				const authHeader = req.headers.get('Authorization') || '';
				if (!authHeader.startsWith('Bearer ')) {
					return addCorsHeaders(Response.json({ error: '未授权，请先获取有效的会话ID' }, { status: 401 }));
				}

				const token = authHeader.substring(7);
				const { valid, payload } = await verifyToken(token);

				if (!valid) {
					return addCorsHeaders(Response.json({ error: '无效的会话ID，请刷新' }, { status: 401 }));
				}

				const { events } = await req.json() as BatchEventRequest;

				if (!Array.isArray(events)) {
					return addCorsHeaders(Response.json({ error: 'events必须是一个数组' }, { status: 400 }));
				}

				// 从JWT令牌中获取会话信息
				const sessionId = payload.jti || payload.sub;
				const deviceId = payload.deviceId;
				const timestamp = new Date().toISOString();

				// 并行处理所有事件
				await Promise.all(events.map(event => {
					// 验证必填字段
					if (!event.eventType) {
						logger.warn('跳过无效事件:', event);
						return Promise.resolve();
					}

					// 构建完整的事件对象
					const fullEvent = {
						...event,
						sessionId, // 使用JWT令牌中的会话ID
						deviceId: deviceId || event.deviceId, // 优先使用JWT中的设备ID
						timestamp: event.timestamp || timestamp
					};

					return logEvent(fullEvent);
				}));

				return addCorsHeaders(Response.json({ status: 'accepted', count: events.length }, { status: 202 }));
			} catch (error) {
				logger.error('处理批量事件时出错:', error);
				return addCorsHeaders(Response.json({ error: '内部服务器错误' }, { status: 500 }));
			}
		},

		// 内部指标端点
		"/internal-metrics": async (req) => {
			// 处理OPTIONS预检请求
			if (req.method === "OPTIONS") {
				return handleOptionsRequest();
			}

			try {
				const metricsData = {
					masterPid: process.pid,
					workers: [], // 如果是单进程或没有简单方法获取其他工作进程信息，则为空
					aggregated: {
						cpu: "0.00", // 在 Bun 中，我们可能需要使用其他方法获取 CPU 使用率
						memory: process.memoryUsage().heapUsed, // 单位是 bytes
						totalRequests: totalRequestsSinceStart
					}
				};
				return addCorsHeaders(Response.json(metricsData));
			} catch (error) {
				logger.error('获取内部指标失败:', error);
				return addCorsHeaders(Response.json({ error: '获取内部指标失败' }, { status: 500 }));
			}
		}
	},

	// 处理未匹配路由的请求和CORS预检请求
	fetch(req) {
		totalRequestsSinceStart++;

		// 处理OPTIONS预检请求
		if (req.method === "OPTIONS") {
			return handleOptionsRequest();
		}

		// 为404响应添加CORS头
		return addCorsHeaders(new Response("Not Found", { status: 404 }));
	},

	// 错误处理
	error(error) {
		logger.error('服务器错误:', error);
		return addCorsHeaders(new Response(`服务器错误: ${error.message}`, {
			status: 500,
			headers: {
				'Content-Type': 'text/plain; charset=utf-8'
			}
		}));
	}
});

// 服务器启动信息始终显示
logger.info(`服务器已启动 - 进程ID: ${process.pid} - 监听 ${server.hostname}:${server.port}`);
console.log(`服务器已启动 - 进程ID: ${process.pid} - 监听 ${server.hostname}:${server.port}`);

// 优雅关闭
const handleShutdown = async (signal: string) => {
	// 使用 Pino 记录关闭信息
	logger.info(`收到 ${signal} 信号，正在关闭服务...`);

	try {
		await server.stop();
		logger.info(`服务器已停止`);

		// 关闭程序日志系统
		await shutdown();
		logger.info(`程序日志已刷新`);

		// 关闭用户行为日志系统
		await shutdownBehaviorLogger();
		logger.info(`用户行为日志已刷新`);

		logger.info(`服务已安全关闭`);
		process.exit(0);
	} catch (error) {
		logger.error(`关闭过程中出错: ${error}`);
		process.exit(1);
	}
};

process.on('SIGTERM', () => handleShutdown('SIGTERM'));
process.on('SIGINT', () => handleShutdown('SIGINT'));
