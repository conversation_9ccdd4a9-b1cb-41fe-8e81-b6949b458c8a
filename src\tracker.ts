/**
 * 用于处理用户行为事件的日志模块
 * 使用 Bun 的文件系统 API 和 ArrayBufferSink 进行高效的日志管理
 * 支持基于大小和日期的日志轮转
 * 实现零拷贝优化，提高性能
 */
import { join } from "node:path";
import { mkdir } from "node:fs/promises";
import { existsSync, mkdirSync } from "node:fs";
import { FileSink } from "bun";
import config from "./config";

// 确保日志目录存在
async function ensureLogDirectory() {
	if (!existsSync(config.tracking.directory)) {
		try {
			await mkdir(config.tracking.directory, { recursive: true });
		} catch (err: any) {
			// logger.error(`创建日志目录失败: ${err.message}`);
			process.exit(1);
		}
	}
}

// 立即执行确保日志目录存在
ensureLogDirectory();

// 解析配置的最大文件大小
function parseMaxFileSize(): number {
	const sizeStr = config.tracking.size;
	const unit = sizeStr.slice(-1).toUpperCase();
	const value = parseInt(sizeStr.slice(0, -1), 10);

	switch (unit) {
		case 'K': return value * 1024;
		case 'M': return value * 1024 * 1024;
		case 'G': return value * 1024 * 1024 * 1024;
		default: return 50 * 1024 * 1024; // 默认50MB
	}
}

// 最大文件大小（字节）
const MAX_FILE_SIZE = parseMaxFileSize(); // 使用配置中的设置，默认为50MB

// 当前日志文件路径
let currentLogFile = '';
// 当前日志文件写入器
let currentWriter: FileSink | null = null;
// 当前日期
let currentDate = new Date().toISOString().split('T')[0];
// 当天的日志文件序号
let currentSequence = 0;
// 当前日志文件的总大小（由于总是创建新文件，等于writer.bytesWritten）
let currentFileTotalSize = 0;

// 基本文件名生成器函数（不包含序列号）
const generateBaseFilename = (time: Date = new Date()): string => {
	const year = time.getFullYear();
	const month = (time.getMonth() + 1).toString().padStart(2, "0");
	const day = time.getDate().toString().padStart(2, "0");
	const dateStr = `${year}-${month}-${day}`;

	// 替换配置的文件名中的 %DATE%
	let baseFilename = config.tracking.filename.replace("%DATE%", dateStr);
	// 如果存在 .log 扩展名，则移除，因为稍后会添加回来
	if (baseFilename.endsWith(".log")) {
		baseFilename = baseFilename.slice(0, -4);
	}

	return baseFilename;
};

// 生成完整文件名（包含序列号）
const generateFilename = (time: Date = new Date(), sequence: number = 0): string => {
	const baseFilename = generateBaseFilename(time);
	const sequenceSuffix = sequence > 0 ? `.${sequence}` : '';
	return join(config.tracking.directory, `${baseFilename}${sequenceSuffix}.log`);
};

// 查找当前日期的下一个可用序列号（用于创建新文件）
function findNextSequence(date: Date): number {
	const baseFilename = generateBaseFilename(date);
	let seq = 0;

	// 检查是否存在序列化的日志文件，找到第一个不存在的序列号
	while (true) {
		const filePath = join(config.tracking.directory, `${baseFilename}${seq > 0 ? `.${seq}` : ''}.log`);
		if (!existsSync(filePath)) {
			break;
		}
		seq++;
	}

	// 返回第一个不存在的序列号（用于创建新文件）
	return seq;
}

// 获取当前日志文件的写入器
function getLogWriter() {
	const now = new Date();
	const today = now.toISOString().split('T')[0];

	// 检查是否需要按日期轮转
	const dateChanged = today !== currentDate;

	// 如果日期变化，重置序列号并更新当前日期
	if (dateChanged) {
		currentDate = today;
		currentSequence = 0;
		currentFileTotalSize = 0;
	}

	// 检查当前文件大小是否超过限制
	const sizeExceeded = currentFileTotalSize >= MAX_FILE_SIZE;

	// 如果需要轮转（日期变化或大小超限）或写入器不存在
	if (dateChanged || sizeExceeded || !currentWriter) {
		// 如果是因为大小超限而轮转，增加序列号
		if (sizeExceeded && !dateChanged) {
			currentSequence++;
			// 重置文件大小计数器
			currentFileTotalSize = 0;
		} else if (!currentWriter) {
			// 如果是首次创建写入器，查找当前日期的下一个可用序列号
			const oldSequence = currentSequence;
			currentSequence = findNextSequence(now);
		}

		// 如果存在旧的写入器，关闭它
		if (currentWriter) {
			currentWriter.flush();
			currentWriter.end();
			currentWriter = null;
		}

		// 创建新的日志文件和写入器
		currentLogFile = generateFilename(now, currentSequence);

		try {
			// 确保日志目录存在
			mkdirSync(config.tracking.directory, { recursive: true });

			// 总是创建新文件，避免覆盖现有文件
			// 由于findNextSequence已经确保了文件不存在，所以可以安全创建
			const file = Bun.file(currentLogFile);
			currentWriter = file.writer({
				highWaterMark: 1024 * 1024, // 1MB 缓冲区
			});

			// 重置文件大小计数器（新文件总是从0开始）
			currentFileTotalSize = 0;

		} catch (error) {
			throw error;
		}
	}

	if (!currentWriter) {
		throw new Error("无法获取有效的日志写入器");
	}

	return currentWriter;
}

// 用于批处理事件的缓冲区
let eventBuffer: any[] = [];
let flushTimer: number | null = null;

/**
 * 记录用户行为事件
 * @param event - 要记录的事件数据
 * @returns Promise<void>
 */
async function logEvent(event: any): Promise<void> {
	try {
		// 如果没有时间戳则添加
		if (!event.timestamp) {
			event.timestamp = new Date().toISOString();
		}

		// 添加到缓冲区
		eventBuffer.push(event);

		// 如果缓冲区超过批处理大小，立即刷新
		if (eventBuffer.length >= config.performance.batchSize) {
			await flushEvents();
		}

		// 如果定时器未设置，则设置刷新定时器
		if (!flushTimer) {
			flushTimer = setTimeout(async () => {
				await flushEvents();
			}, config.performance.flushInterval) as unknown as number;
		}
	} catch (error) {
		throw error;
	}
}

/**
 * 将缓冲的事件刷新到日志文件，使用零拷贝优化
 */
async function flushEvents(): Promise<void> {
	if (eventBuffer.length === 0) return;

	// 清除定时器
	if (flushTimer) {
		clearTimeout(flushTimer);
		flushTimer = null;
	}

	// 获取要刷新的事件并重置缓冲区
	const eventsToFlush = [...eventBuffer];
	eventBuffer = [];

	try {
		const writer = getLogWriter();

		// 使用 TextEncoder 进行零拷贝写入
		const encoder = new TextEncoder();

		// 处理要写入的事件
		let remainingEvents = [];

		// 逐个处理事件，检查是否会导致文件大小超标
		for (let i = 0; i < eventsToFlush.length; i++) {
			// 检查添加此事件是否会导致文件大小超标
			if (currentFileTotalSize > MAX_FILE_SIZE) {
				// 如果会超标，将此事件及后续所有事件放回缓冲区，然后跳出循环
				remainingEvents = eventsToFlush.slice(i);
				break; // 立即跳出循环，不再处理后续事件
			}

			const event = eventsToFlush[i];

			// 将事件转换为JSON字符串并编码
			const eventStr = JSON.stringify(event) + "\n";
			const eventBuffer = encoder.encode(eventStr);
			const eventSize = eventBuffer.byteLength;

			// 如果不会超标，添加到要写入的数据中
			writer.write(eventBuffer);
			currentFileTotalSize += eventSize;
		}

		// 强制刷新到磁盘
		writer.flush();

		// 如果有剩余事件，放回缓冲区等待下次处理
		if (remainingEvents.length > 0) {
			eventBuffer = [...remainingEvents, ...eventBuffer];
		}
	} catch (error) {
		// 如果写入失败，将事件放回缓冲区
		eventBuffer = [...eventsToFlush, ...eventBuffer];
		throw error;
	}
}

/**
 * 确保在关闭前刷新所有事件
 */
async function shutdown(): Promise<void> {
	try {
		await flushEvents();
		if (currentWriter) {
			currentWriter.flush();
			currentWriter.end();
			currentWriter = null;
		}
	} catch (error) {
	}
}

// 确保在进程退出时刷新事件
process.on("beforeExit", async () => {
	try {
		await shutdown();
	} catch (error) {
	}
});

export {
	logEvent,
	flushEvents,
	shutdown,
};
