/**
 * Bun 打包配置文件
 * 提供更灵活的打包选项
 */
import { BuildConfig, BunPlugin, PluginBuilder } from "bun";

// 获取构建时间
const buildTime = new Date().toISOString();

// 添加构建时间的插件
const buildTimePlugin: BunPlugin = {
  name: "build-time-plugin",
  setup(build: PluginBuilder) {
    build.onLoad({ filter: /server\.ts$/ }, async (args: { path: string }) => {
      const source = await Bun.file(args.path).text();
      const newSource = `/**
 * 构建时间: ${buildTime}
 */
${source}`;

      return {
        contents: newSource,
        loader: "ts",
      };
    });
  },
};

// 执行打包
async function main() {
  // 读取环境变量
  const isProduction = process.env.NODE_ENV === "production";
  console.log(`构建模式: ${isProduction ? "生产环境" : "开发环境"}`);

  // 打包配置
  const config: BuildConfig = {
    entrypoints: ["./src/server.ts"],
    outdir: "./dist",
    target: "bun",
    minify: isProduction,
    sourcemap: "external",
    plugins: [buildTimePlugin],
    define: {
      "process.env.BUILD_TIME": JSON.stringify(buildTime),
      "process.env.VERSION": JSON.stringify(process.env.npm_package_version || "1.0.0"),
    },
  };

  // 执行打包
  const result = await Bun.build(config);

  // 输出结果
  console.log("\n打包完成!");
  console.log("输出文件:");
  for (const output of result.outputs) {
    console.log(`- ${output.path} (${(output.size / 1024).toFixed(2)} KB)`);
  }
}

// 运行打包
main().catch(err => {
  console.error("打包失败:", err);
  process.exit(1);
});
