/**
 * 用户行为跟踪服务器的配置
 * 使用 Bun 特有的 API 优化
 */

export interface ServerConfig {
	port: number;
	host: string;
	development: boolean;
}

export interface LoggingConfig {
	directory: string;
	filename: string;
	frequency: string;
	size: string;
	maxFiles: number;
	compress: boolean;
}

export interface PerformanceConfig {
	workers: number;
	batchSize: number;
	flushInterval: number;
}

export interface JwtConfig {
	secret: string;
	expiresIn: number; // 秒
}

export interface Config {
	server: ServerConfig;
	logging: LoggingConfig;
	tracking: LoggingConfig;
	performance: PerformanceConfig;
	jwt: JwtConfig;
}

// 生成随机JWT密钥
const generateJwtSecret = (): string => {
	return crypto.randomUUID();
};

// 获取环境变量，使用 Bun.env 替代 process.env
const getEnv = (key: string, defaultValue: string): string => {
	return Bun.env[key] || defaultValue;
};

const config: Config = {
	// 服务器配置
	server: {
		port: parseInt(getEnv("PORT", "3003"), 10),
		host: getEnv("HOST", "0.0.0.0"),
		development: getEnv("NODE_ENV", "development") !== "production"
	},

	// 程序日志配置 (使用 Pino)
	logging: {
		// 存储日志文件的目录
		directory: "./logs/app",
		// 日志文件的基本文件名
		filename: "app-%DATE%.log",
		// 日志轮转频率（1d = 每天，1h = 每小时）
		frequency: "1d",
		// 轮转前每个日志文件的最大大小
		size: "50M",
		// 保留的最大日志文件数
		maxFiles: 30,
		// 压缩轮转的日志
		compress: false,
	},

	// 用户行为日志配置 (使用 Bun 文件系统)
	tracking: {
		// 存储日志文件的目录
		directory: "./logs/behavior",
		// 日志文件的基本文件名
		filename: "user-%DATE%.log",
		// 日志轮转频率（1d = 每天，1h = 每小时）
		frequency: "1d",
		// 轮转前每个日志文件的最大大小
		size: "50M",
		// 保留的最大日志文件数
		maxFiles: 30,
		// 压缩轮转的日志
		compress: false,
	},

	// 性能调优
	performance: {
		// 设置为0表示使用所有可用的CPU核心
		workers: 0,
		// 批处理中处理的最大事件数
		batchSize: 1000,
		// 刷新间隔（毫秒）
		flushInterval: 1000,
	},

	// JWT配置
	jwt: {
		secret: getEnv("JWT_SECRET", generateJwtSecret()),
		expiresIn: 24 * 60 * 60, // 24小时
	}
};

export default config;
